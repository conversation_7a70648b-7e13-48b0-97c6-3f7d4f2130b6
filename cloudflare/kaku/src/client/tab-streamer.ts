import { BoundingBox, Viewport } from '../common/types';
import { CaptchaDetectorCallback } from './types';

/**
 * Generic Tab Streamer - A reusable WebRTC-based screen streaming utility
 *
 * Supports multiple streaming modes:
 * - FULL_SCREEN: Stream entire tab/window content
 * - CROPPED: Stream specific bounding box region (for captcha detection)
 *
 * Features:
 * - WebRTC-based streaming for optimal performance
 * - Configurable frame processing and transformation
 * - Input field detection and coordinate mapping
 * - Extensible callback system for frame processing
 */

export type StreamingMode = 'FULL_SCREEN' | 'CROPPED';

export interface StreamingConfig {
  mode: StreamingMode;
  frameRate: number;
  debug: boolean;
  cropBox?: BoundingBox;
  enableInputDetection?: boolean;
}

export interface FrameProcessorCallback {
  (frame: VideoFrame, dimensions: { width: number; height: number }): Promise<boolean>;
}

export interface StreamerCallbacks {
  onFrameProcessed?: FrameProcessorCallback;
  onInputFieldsDetected?: (inputFields: BoundingBox[]) => void;
}

// Extend window interface for tab streamer
declare global {
  interface Window {
    tabStreamer: {
      init: (
        wsEndpoint: string,
        viewport: Viewport,
        streamingConfig?: Partial<StreamingConfig>,
        streamerCallbacks?: StreamerCallbacks,
      ) => Promise<string>;
      start: (viewport: Viewport, cropBox?: BoundingBox) => Promise<void>;
      stopStreaming: () => void;
      updateConfig: (newConfig: Partial<StreamingConfig>) => void;
      updateCropBox: (newCropBox: BoundingBox) => void;
      registerFrameProcessor: (callback: FrameProcessorCallback) => void;
      pauseFrameSending: () => void;
      resumeFrameSending: () => void;
      getConfig: () => StreamingConfig;
      isInitialized: () => boolean;
      isStreaming: () => boolean;
    };
  }
}

(function () {
  const defaultConfig: StreamingConfig = {
    mode: 'FULL_SCREEN',
    frameRate: 15,
    debug: true,
    enableInputDetection: true,
  };

  let config: StreamingConfig = { ...defaultConfig };
  let callbacks: StreamerCallbacks = {};

  // Connection state
  let socket: WebSocket | null = null;
  let pc: RTCPeerConnection | null = null;
  let screenStream: MediaStream | null = null;
  let inputChannel: RTCDataChannel | undefined;
  let isInitialized = false;
  let isStreaming = false;

  // Streaming state
  let frameWidth = 800;
  let frameHeight = 600;
  let cropRegion: BoundingBox = { x: 0, y: 0, width: 0, height: 0 };
  let isSendingFrames = true;
  let currentInputBoxRects: BoundingBox[] = [];

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[kazeel][tab-streamer]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[kazeel][tab-streamer]', ...args);
  }

  /**
   * Initialize the tab streamer with configuration
   */
  async function init(
    wsEndpoint: string,
    viewport: Viewport,
    streamingConfig?: Partial<StreamingConfig>,
    streamerCallbacks?: StreamerCallbacks,
  ): Promise<string> {
    if (isInitialized) {
      log('Tab streamer already initialized');
      return 'ALREADY_INITIALIZED';
    }
    try {
      // Merge configuration
      config = { ...defaultConfig, ...streamingConfig };
      callbacks = { ...streamerCallbacks };

      log('🔧 [init] Starting tab streamer initialization...');
      log('🔧 [init] Mode:', config.mode);
      log('🔧 [init] WebSocket endpoint:', wsEndpoint);
      log('🔧 [init] Viewport:', viewport);

      // Wait for browser controller to be available
      log('🔧 [init] Step 1: Waiting for browser controller...');
      // await waitForBrowserController();
      log('✅ [init] Browser controller is ready');

      // Setup WebSocket connection
      log('🔧 [init] Step 2: Setting up WebSocket connection...');
      await connectToSocket(wsEndpoint);
      log('✅ [init] WebSocket connection established');

      // Setup WebRTC connection
      log('🔧 [init] Step 3: Setting up WebRTC connection...');
      await connectToWebRTC();
      log('✅ [init] WebRTC connection established');

      isInitialized = true;
      log('✅ [init] Tab streamer initialization completed successfully');
      return 'SUCCESS';
    } catch (err: any) {
      throw new Error(
        `[kazeel][tab-streamer] Failed to initialize: ${err.message} | Error stack: ${err.stack}`,
      );
    }
  }

  /**
   * Start streaming with the configured mode
   */
  async function start(viewport: Viewport, cropBox?: BoundingBox): Promise<void> {
    if (!isInitialized) {
      throw new Error(`[kazeel][tab-streamer] Tab streamer not initialized`);
    }

    try {
      log('Starting tab streamer with mode:', config.mode);
      log('Viewport:', viewport);

      if (config.mode === 'CROPPED') {
        if (!cropBox) {
          // For captcha mode, get initial bounding box from TF detector
          if (window.tfCaptchaDetector) {
            log('Getting initial bounding box from TF detector...');
            cropBox = await window.tfCaptchaDetector.getInitialBoundingBox(viewport);
            log('Initial crop box:', cropBox);
          } else {
            throw new Error('Crop box required for CROPPED mode when TF detector not available');
          }
        }
        config.cropBox = cropBox;
      } else {
        // For full screen mode, use entire viewport
        config.cropBox = {
          x: 0,
          y: 0,
          width: viewport.width,
          height: viewport.height,
        };
      }

      log('Starting streaming with configuration...');
      await startStreaming(viewport, config.cropBox);

      isStreaming = true;

      log('Tab streamer started successfully');
    } catch (err: any) {
      throw new Error(`[kazeel][tab-streamer] Failed to start streaming: ${err.message}`);
    }
  }

  /**
   * Stop streaming and clean up resources
   */
  function stopStreaming(): void {
    log('Stopping tab streamer');

    try {
      if (screenStream) {
        const tracks = screenStream.getTracks();
        if (tracks && tracks.length > 0) {
          log('Stopping ' + tracks.length + ' screen tracks');
          tracks.forEach((track) => {
            if (track.readyState === 'live') {
              track.stop();
            }
          });
        }
        screenStream = null;
      }

      if (socket && socket.readyState === WebSocket.OPEN) {
        try {
          socket.close();
        } catch (socketErr) {
          error('Error closing socket:', socketErr);
        }
      }

      if (pc) {
        try {
          pc.close();
        } catch (pcErr) {
          error('Error closing peer connection:', pcErr);
        }
        pc = null;
      }

      socket = null;
      isStreaming = false;

      log('Tab streamer stopped successfully');
    } catch (err) {
      error('Error during streaming cleanup:', err);
    }
  }

  /**
   * Update streaming configuration
   */
  function updateConfig(newConfig: Partial<StreamingConfig>): void {
    config = { ...config, ...newConfig };
    log('Configuration updated:', config);
  }

  /**
   * Update crop box for CROPPED mode
   */
  function updateCropBox(newCropBox: BoundingBox): void {
    config.cropBox = newCropBox;
    cropRegion = {
      x: Math.floor(newCropBox.x / 2) * 2,
      y: Math.floor(newCropBox.y / 2) * 2,
      width: Math.floor(newCropBox.width / 2) * 2,
      height: Math.floor(newCropBox.height / 2) * 2,
    };

    log('Crop box updated:', newCropBox);

    // Detect input fields if enabled
    if (config.enableInputDetection) {
      detectInputFieldsInViewport(newCropBox);
    }
  }

  /**
   * Register frame processor callback
   */
  function registerFrameProcessor(callback: FrameProcessorCallback): void {
    callbacks.onFrameProcessed = callback;
    log('Frame processor callback registered');
  }

  /**
   * Register captcha detector callback (for backward compatibility)
   */
  function registerCaptchaDetectorCallback(callback: CaptchaDetectorCallback): void {
    // Convert CaptchaDetectorCallback to FrameProcessorCallback
    const frameCallback: FrameProcessorCallback = async (frame: VideoFrame, dimensions) => {
      // For captcha detector compatibility, we need to convert VideoFrame to RGBA buffer
      // This is a simplified version - in practice, you'd need proper YUV to RGBA conversion
      try {
        // Create a canvas to extract RGBA data
        const canvas = document.createElement('canvas');
        canvas.width = dimensions.width;
        canvas.height = dimensions.height;
        const ctx = canvas.getContext('2d');

        if (ctx) {
          // Draw the video frame to canvas (this is a simplified approach)
          // In practice, you'd need proper VideoFrame to canvas conversion
          const imageData = ctx.createImageData(dimensions.width, dimensions.height);
          await callback(new Uint8Array(imageData.data), dimensions);
        }

        return false; // Don't skip frame
      } catch (error) {
        console.error('Error in captcha detector callback wrapper:', error);
        return false;
      }
    };

    callbacks.onFrameProcessed = frameCallback;
    log('Captcha detector callback registered (compatibility mode)');
  }

  /**
   * Control frame sending (for pause/resume functionality)
   */
  function pauseFrameSending(): void {
    if (!isSendingFrames) return;
    isSendingFrames = false;
    log('Paused sending frames to client');
  }

  function resumeFrameSending(): void {
    if (isSendingFrames) return;
    isSendingFrames = true;
    log('Resumed sending frames to client');
  }

  /**
   * Start the actual streaming process
   */
  async function startStreaming(viewport: Viewport, streamingBox: BoundingBox): Promise<void> {
    // Update frame dimensions
    frameWidth = viewport.width;
    frameHeight = viewport.height;

    log(`Updated frame dimensions to ${frameWidth}x${frameHeight}`);

    // Get display media stream
    log('Requesting display media stream...');
    try {
      screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: config.frameRate,
          width: viewport.width,
          height: viewport.height,
        },
        //@ts-ignore
        preferCurrentTab: true,
      });
      log('Display media stream obtained successfully');
    } catch (err) {
      throw new Error(`[kazeel][tab-streamer] Failed to get display media stream: ${err}`);
    }

    // Set up browser metrics if available
    if (window.browserController) {
      await window.browserController.setupBrowserMetrics(viewport);
    }

    // Set up stream processing
    const videoTrack = screenStream.getVideoTracks()[0];
    const processor = new MediaStreamTrackProcessor({ track: videoTrack });
    const generator = new MediaStreamTrackGenerator({ kind: 'video' });

    // Update crop region for alignment
    cropRegion = {
      x: Math.floor(streamingBox.x / 2) * 2,
      y: Math.floor(streamingBox.y / 2) * 2,
      width: Math.floor(streamingBox.width / 2) * 2,
      height: Math.floor(streamingBox.height / 2) * 2,
    };

    // Create transform stream based on mode
    const transform = new TransformStream({
      async transform(frame, controller) {
        // Process frame with callback if registered
        if (callbacks.onFrameProcessed) {
          try {
            const shouldSkip = await callbacks.onFrameProcessed(frame, {
              width: frameWidth,
              height: frameHeight,
            });
            if (shouldSkip) {
              frame.close();
              return;
            }
          } catch (err) {
            error('Error in frame processor callback:', err);
          }
        }

        if (isSendingFrames) {
          if (config.mode === 'FULL_SCREEN') {
            // For full screen mode, pass through the frame as-is
            controller.enqueue(frame);
          } else {
            // For cropped mode, crop the frame
            const { codedWidth, codedHeight } = frame;
            const safeCropRegion = {
              x: Math.min(cropRegion.x, codedWidth),
              y: Math.min(cropRegion.y, codedHeight),
              width: Math.min(cropRegion.width, codedWidth - cropRegion.x),
              height: Math.min(cropRegion.height, codedHeight - cropRegion.y),
            };

            try {
              const cropped = new VideoFrame(frame, {
                visibleRect: safeCropRegion,
                displayWidth: safeCropRegion.width,
                displayHeight: safeCropRegion.height,
                timestamp: frame.timestamp,
                duration: frame.duration,
              });
              controller.enqueue(cropped);
            } catch (e) {
              error('Failed to crop frame:', e);
            }
          }
        }

        frame.close();
      },
    });

    processor.readable.pipeThrough(transform).pipeTo(generator.writable);
    //@ts-ignore
    const stream = new MediaStream([generator]);
    //@ts-ignore
    pc?.addTrack(videoTrack, screenStream);
    log('Track added to peer connection');
    // Detect input fields if enabled
    if (config.enableInputDetection) {
      setTimeout(() => {
        log('🔍 [STARTUP] Performing initial input field detection...');
        detectInputFieldsInViewport(streamingBox);
      }, 1000);
    }
  }

  /**
   * Detect input fields in viewport (simplified version)
   */
  async function detectInputFieldsInViewport(viewportArea: BoundingBox): Promise<void> {
    try {
      log('🔍 [INPUT DETECTION] Detecting input fields within viewport:', viewportArea);

      // Simple input field detection for the current document
      const inputFields: BoundingBox[] = [];
      const inputElements = document.querySelectorAll(
        'input:not([type="button"]):not([type="submit"]):not([type="reset"]), textarea, select',
      );

      inputElements.forEach((element) => {
        const rect = element.getBoundingClientRect();
        const absoluteRect = {
          x: rect.left + window.scrollX,
          y: rect.top + window.scrollY,
          width: rect.width,
          height: rect.height,
        };

        // Check if input is within viewport
        if (
          absoluteRect.x >= viewportArea.x &&
          absoluteRect.y >= viewportArea.y &&
          absoluteRect.x + absoluteRect.width <= viewportArea.x + viewportArea.width &&
          absoluteRect.y + absoluteRect.height <= viewportArea.y + viewportArea.height
        ) {
          inputFields.push(absoluteRect);
        }
      });

      currentInputBoxRects = inputFields;
      callbacks.onInputFieldsDetected?.(inputFields);

      log('✅ [INPUT DETECTION] Detected', inputFields.length, 'input fields');
    } catch (err) {
      error('❌ [INPUT DETECTION] Failed to detect input fields:', err);
    }
  }

  /**
   * Wait for browser controller to be available
   */
  async function waitForBrowserController(): Promise<void> {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max wait

    while (attempts < maxAttempts) {
      if (!window.browserController) {
        log(`Waiting for browser controller... (attempt ${attempts + 1}/${maxAttempts})`);
      } else {
        try {
          log(
            `Browser controller found, testing initialization... (attempt ${attempts + 1}/${maxAttempts})`,
          );
          await window.browserController.ping();
          log('Browser controller is available and initialized');
          return;
        } catch (error: any) {
          log(
            `Browser controller not yet initialized: ${error.message} (attempt ${attempts + 1}/${maxAttempts})`,
          );
        }
      }

      await new Promise((resolve) => setTimeout(resolve, 100));
      attempts++;
    }

    if (!window.browserController) {
      throw new Error(`[kazeel][tab-streamer] Browser controller not available after waiting`);
    }
  }

  /**
   * Connect to WebSocket
   */
  async function connectToSocket(wsEndpoint: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        log('🔧 [connectToSocket] Connecting to WebSocket:', wsEndpoint);

        if (!wsEndpoint) {
          throw new Error(`[kazeel][tab-streamer] WebSocket endpoint is required`);
        }

        socket = new WebSocket(wsEndpoint);
        window.socket = socket; // Expose for compatibility

        socket.addEventListener('open', () => {
          log('✅ [connectToSocket] WebSocket connected successfully');
          resolve();
        });

        socket.addEventListener('error', (e) => {
          reject(
            new Error(
              `[kazeel][tab-streamer] WebSocket connection failed: ${e.type || 'Unknown error'}`,
            ),
          );
        });

        socket.addEventListener('close', (e) => {
          log('🔌 [connectToSocket] WebSocket connection closed:', e.code, e.reason);
        });

        setupSocketMessageHandlers();
        log('✅ [connectToSocket] Message handlers set up');
      } catch (err) {
        error('❌ [connectToSocket] Error setting up WebSocket:', err);
        reject(err);
      }
    });
  }

  /**
   * Connect to WebRTC
   */
  async function connectToWebRTC(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        log('🔧 [connectToWebRTC] Setting up WebRTC peer connection...');

        pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.cloudflare.com:3478' },
            {
              urls: 'turn:relay1.expressturn.com:3478',
              username: 'ef89RMU4SHUQMSOUU9',
              credential: 'jvkMMnQxWX4Qrhe3',
            },
          ],
        });

        pc.onicecandidate = (event) => {
          if (event.candidate && socket?.readyState === WebSocket.OPEN) {
            log('🔧 [connectToWebRTC] Sending ICE candidate');
            socket.send(JSON.stringify({ type: 'candidate', candidate: event.candidate }));
          }
        };

        pc.onconnectionstatechange = () => {
          log('🔧 [connectToWebRTC] WebRTC connection state:', pc?.connectionState);
          if (pc?.connectionState === 'connected') {
            log('✅ [connectToWebRTC] WebRTC connection established successfully');
            resolve();
          } else if (pc?.connectionState === 'failed') {
            error('❌ [connectToWebRTC] WebRTC connection failed');
            reject(new Error('WebRTC connection failed'));
          }
        };

        log('✅ [connectToWebRTC] WebRTC peer connection setup completed');
        resolve();
      } catch (err) {
        error('❌ [connectToWebRTC] Error setting up WebRTC:', err);
        reject(err);
      }
    });
  }

  /**
   * Set up WebSocket message handlers
   */
  function setupSocketMessageHandlers(): void {
    socket?.addEventListener('message', async (event) => {
      let msg;
      try {
        msg = JSON.parse(event.data);
      } catch (err) {
        // log('❌ [setupSocketMessageHandlers] Failed to handle WebSocket message:', err);
        return;
      }
      switch (msg.type) {
        case 'offer':
          await pc?.setRemoteDescription(new RTCSessionDescription(msg.offer));
          const answer = await pc?.createAnswer();
          await pc?.setLocalDescription(answer);
          socket?.send(JSON.stringify({ type: 'answer', answer }));
          break;
        case 'answer':
          await pc?.setRemoteDescription(new RTCSessionDescription(msg.answer));
          break;
        case 'candidate':
          await pc?.addIceCandidate(new RTCIceCandidate(msg.candidate));
          break;
        case 'ready':
          log('🔧 [ready] Creating input channel before offer...');
          createInputChannel();
          const offer = await pc?.createOffer();
          await pc?.setLocalDescription(offer);
          socket?.send(JSON.stringify({ type: 'offer', offer }));
          log('✅ [ready] Offer sent with input channel');
          break;
        case 'interactivity-status':
          if (msg.status === 'paused') {
            pauseFrameSending();
          } else if (msg.status === 'enabled') {
            resumeFrameSending();
          } else if (msg.status === 'completed') {
            log('Streaming completed, stopping');
            stopStreaming();
          }
          break;
      }
    });

    socket?.addEventListener('error', (e) => {
      error('WebSocket error:', e);
    });
  }

  /**
   * Create input data channel for user interactions
   */
  function createInputChannel(): void {
    try {
      log('🔧 [createInputChannel] Creating input data channel...');
      inputChannel = pc?.createDataChannel('inputEvents', {
        ordered: true,
        maxRetransmits: 3,
      });

      if (inputChannel) {
        inputChannel.onopen = () => {
          log('✅ [createInputChannel] Input channel opened successfully');
        };

        inputChannel.onclose = () => {
          log('🔌 [createInputChannel] Input channel closed');
        };

        inputChannel.onerror = (errorEvent) => {
          error('❌ [createInputChannel] Input channel error:', errorEvent);
        };

        inputChannel.onmessage = async (event) => {
          try {
            log('📨 [createInputChannel] Received message:', event.data);
            const data = JSON.parse(event.data);

            if (data.type === 'request-frame') {
              await window.browserController?.requestNewFrame();
              return;
            }

            // Delegate input handling to browser controller
            if (window.browserController) {
              await window.browserController.handleInputEvent(data);
            } else {
              error('Browser controller not available for input handling');
            }
          } catch (err) {
            error('Failed to handle input event:', err);
          }
        };

        log('✅ [createInputChannel] Input channel setup completed');
      }
    } catch (err) {
      error('Failed to create input channel:', err);
    }
  }
  // Export the public API
  window.tabStreamer = {
    init,
    start,
    stopStreaming,
    updateConfig,
    updateCropBox,
    registerFrameProcessor,
    pauseFrameSending,
    resumeFrameSending,
    getConfig: () => ({ ...config }),
    isInitialized: () => isInitialized,
    isStreaming: () => isStreaming,
  };

  // Backward compatibility - expose as screenCropper for existing code
  window.screenCropper = {
    init: (wsEndpoint: string, viewport: Viewport) =>
      init(wsEndpoint, viewport, { mode: 'CROPPED' }),
    start: (viewport: Viewport) => start(viewport),
    stopStreaming,
    updateCropBox,
    registerCaptchaDetectorCallback,
    startCapturingForCaptchaDetector: () => {}, // No-op for compatibility
    stopCapturingForCaptchaDetector: () => {}, // No-op for compatibility
    pauseFrameSending,
    resumeFrameSending,
  };
})();
